import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculationAgeBasedMaximum.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/agebasedmaximums/{baseForCalculationAgeBasedMaximumId}",
    method: "PATCH",
    params: {
        baseForCalculationAgeBasedMaximumId: entityData.baseForCalculationAgeBasedMaximumIds.QA_BFC_AgeBasedMaximum_PATCH_CLA.year2025.ageBasedMaximum_5,
    },
    modules: {
        methodNotAllowed: ["GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,
        token: "QA_PayrollConfiguration1",
        body: { "maximum": 256 },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumId should return 400 and the correct message for invalid Maximum value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationAgeBasedMaximumId: entityData.baseForCalculationAgeBasedMaximumIds.QA_BFC_AgeBasedMaximum_PATCH_CLA.year2025.ageBasedMaximum_5,
                },
                timeout: 120000,
                body: { "maximum": 1000000},
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "ModelStateValidationError",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumId should return 403 when baseForCalculationAgeBasedMaximumId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationAgeBasedMaximumId: sharedData.nonEntityGuid,
                },
                body: { "maximum": 256 },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
