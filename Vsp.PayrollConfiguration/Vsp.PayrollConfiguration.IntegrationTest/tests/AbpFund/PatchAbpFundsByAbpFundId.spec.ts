import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./AbpFund.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "abpfunds/{abpFundId}",
    method: "PATCH",
    params: {
        baseForCalculationAgeBasedMinimumId: entityData.abpFundIds.QA_AbpFund_PATCH_CLA.year2025.abpFund_2,
    },
    modules: {
        methodNotAllowed: ["GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "totalContribution": 0.000,
            "employmentContribution": 0.000,
            "franchise": 0.00,
            "franchiseUpToAge40": 0.00,
            "franchiseUpToAge50": 0.00
        },
        tokens: {
            QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
        },